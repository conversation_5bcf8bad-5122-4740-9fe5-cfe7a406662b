@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: oklch(0.9730 0.0133 286.1503);
    --foreground: oklch(0.3015 0.0572 282.4176);
    --card: oklch(1.0000 0 0);
    --card-foreground: oklch(0.3015 0.0572 282.4176);
    --popover: oklch(1.0000 0 0);
    --popover-foreground: oklch(0.3015 0.0572 282.4176);
    --primary: oklch(0.5417 0.1790 288.0332);
    --primary-foreground: oklch(1.0000 0 0);
    --secondary: oklch(0.9174 0.0435 292.6901);
    --secondary-foreground: oklch(0.4143 0.1039 288.1742);
    --muted: oklch(0.9580 0.0133 286.1454);
    --muted-foreground: oklch(0.5426 0.0465 284.7435);
    --accent: oklch(0.9221 0.0373 262.1410);
    --accent-foreground: oklch(0.3015 0.0572 282.4176);
    --destructive: oklch(0.6861 0.2061 14.9941);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(0.9115 0.0216 285.9625);
    --input: oklch(0.9115 0.0216 285.9625);
    --ring: oklch(0.5417 0.1790 288.0332);
    --radius: 0.5rem;
    --chart-1: oklch(0.5417 0.1790 288.0332);
    --chart-2: oklch(0.7042 0.1602 288.9880);
    --chart-3: oklch(0.5679 0.2113 276.7065);
    --chart-4: oklch(0.6356 0.1922 281.8054);
    --chart-5: oklch(0.4509 0.1758 279.3838);

    /* Sidebar variables */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: oklch(0.3015 0.0572 282.4176);
    --sidebar-primary: oklch(0.5417 0.1790 288.0332);
    --sidebar-primary-foreground: oklch(1.0000 0 0);
    --sidebar-accent: oklch(0.9221 0.0373 262.1410);
    --sidebar-accent-foreground: oklch(0.3015 0.0572 282.4176);
    --sidebar-border: oklch(0.9115 0.0216 285.9625);
    --sidebar-ring: oklch(0.5417 0.1790 288.0332);
    --sidebar: oklch(0.9580 0.0133 286.1454);
    --font-sans: Inter, sans-serif;
    --font-serif: Georgia, serif;
    --font-mono: JetBrains Mono, monospace;
    --shadow-color: hsl(240 30% 25%);
    --shadow-opacity: 0.12;
    --shadow-blur: 10px;
    --shadow-spread: 0px;
    --shadow-offset-x: 0px;
    --shadow-offset-y: 4px;
    --letter-spacing: 0em;
    --spacing: 0.25rem;
    --shadow-2xs: 0px 4px 10px 0px hsl(240 30% 25% / 0.06);
    --shadow-xs: 0px 4px 10px 0px hsl(240 30% 25% / 0.06);
    --shadow-sm: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12);
    --shadow: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12);
    --shadow-md: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 2px 4px -1px hsl(240 30% 25% / 0.12);
    --shadow-lg: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 4px 6px -1px hsl(240 30% 25% / 0.12);
    --shadow-xl: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 8px 10px -1px hsl(240 30% 25% / 0.12);
    --shadow-2xl: 0px 4px 10px 0px hsl(240 30% 25% / 0.30);
    --tracking-normal: 0em;
  }

  [data-theme="dark"] {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* Sidebar variables */
    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 217.2 91.2% 59.8%;
    --sidebar-primary-foreground: 222.2 84% 4.9%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .theme {
    --font-sans: Inter, sans-serif;
    --font-mono: JetBrains Mono, monospace;
    --font-serif: Georgia, serif;
    --radius: 0.5rem;
    --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
    --tracking-tight: calc(var(--tracking-normal) - 0.025em);
    --tracking-wide: calc(var(--tracking-normal) + 0.025em);
    --tracking-wider: calc(var(--tracking-normal) + 0.05em);
    --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  }
  .dark {
    --background: oklch(0.1743 0.0227 283.7998);
    --foreground: oklch(0.9185 0.0257 285.8834);
    --card: oklch(0.2284 0.0384 282.9324);
    --card-foreground: oklch(0.9185 0.0257 285.8834);
    --popover: oklch(0.2284 0.0384 282.9324);
    --popover-foreground: oklch(0.9185 0.0257 285.8834);
    --primary: oklch(0.7162 0.1597 290.3962);
    --primary-foreground: oklch(0.1743 0.0227 283.7998);
    --secondary: oklch(0.3139 0.0736 283.4591);
    --secondary-foreground: oklch(0.8367 0.0849 285.9111);
    --muted: oklch(0.2710 0.0621 281.4377);
    --muted-foreground: oklch(0.7166 0.0462 285.1741);
    --accent: oklch(0.3354 0.0828 280.9705);
    --accent-foreground: oklch(0.9185 0.0257 285.8834);
    --destructive: oklch(0.6861 0.2061 14.9941);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(0.3261 0.0597 282.5832);
    --input: oklch(0.3261 0.0597 282.5832);
    --ring: oklch(0.7162 0.1597 290.3962);
    --chart-1: oklch(0.7162 0.1597 290.3962);
    --chart-2: oklch(0.6382 0.1047 274.9117);
    --chart-3: oklch(0.7482 0.1235 244.7492);
    --chart-4: oklch(0.7124 0.0977 186.6761);
    --chart-5: oklch(0.7546 0.1831 346.8124);
    --radius: 0.5rem;
    --sidebar: oklch(0.2284 0.0384 282.9324);
    --sidebar-foreground: oklch(0.9185 0.0257 285.8834);
    --sidebar-primary: oklch(0.7162 0.1597 290.3962);
    --sidebar-primary-foreground: oklch(0.1743 0.0227 283.7998);
    --sidebar-accent: oklch(0.3354 0.0828 280.9705);
    --sidebar-accent-foreground: oklch(0.9185 0.0257 285.8834);
    --sidebar-border: oklch(0.3261 0.0597 282.5832);
    --sidebar-ring: oklch(0.7162 0.1597 290.3962);
    --font-sans: Inter, sans-serif;
    --font-serif: Georgia, serif;
    --font-mono: JetBrains Mono, monospace;
    --shadow-color: hsl(240 30% 25%);
    --shadow-opacity: 0.12;
    --shadow-blur: 10px;
    --shadow-spread: 0px;
    --shadow-offset-x: 0px;
    --shadow-offset-y: 4px;
    --letter-spacing: 0em;
    --spacing: 0.25rem;
    --shadow-2xs: 0px 4px 10px 0px hsl(240 30% 25% / 0.06);
    --shadow-xs: 0px 4px 10px 0px hsl(240 30% 25% / 0.06);
    --shadow-sm: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12);
    --shadow: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12);
    --shadow-md: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 2px 4px -1px hsl(240 30% 25% / 0.12);
    --shadow-lg: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 4px 6px -1px hsl(240 30% 25% / 0.12);
    --shadow-xl: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 8px 10px -1px hsl(240 30% 25% / 0.12);
    --shadow-2xl: 0px 4px 10px 0px hsl(240 30% 25% / 0.30);
  }
  body {
    letter-spacing: var(--tracking-normal);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground transition-colors duration-300;
  }
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-pulse-glow {
  animation: pulse-glow 2s infinite;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-primary;
}

/* Smooth transitions for theme switching */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Gaming-specific enhancements */
.gaming-card {
  backdrop-filter: blur(4px);
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="dark"] .gaming-card {
  background-color: rgba(31, 41, 55, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.gaming-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
}

.gaming-gradient {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(139, 92, 246, 0.1) 50%,
    rgba(6, 182, 212, 0.1) 100%);
}

/* Button enhancements */
.btn {
  @apply transition-all duration-200 ease-in-out;
}

.btn:hover {
  transform: translateY(-1px);
}

.btn:active {
  transform: translateY(0);
}

/* Card hover effects */
.card {
  @apply transition-all duration-300 ease-in-out;
}

.card:hover {
  @apply shadow-lg;
  transform: translateY(-2px);
}

/* Progress bar animations */
.progress {
  overflow: hidden;
}

.progress::-webkit-progress-bar {
  @apply bg-muted rounded-full;
}

.progress::-webkit-progress-value {
  @apply bg-primary rounded-full;
  transition: width 0.5s ease-in-out;
}
