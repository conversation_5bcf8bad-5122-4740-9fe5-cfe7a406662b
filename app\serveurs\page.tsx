"use client"

import { useState } from "react"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Card, CardHeader, CardTitle, CardContent, CardDescription, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Users,
  Wifi,
  Clock,
  Shield,
  Sword,
  Map,
  Server,
  Activity,
  Globe,
  Zap,
  Calendar,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  XCircle,
  RefreshCw
} from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { motion } from "framer-motion"

// Données des serveurs
const serversData = [
  {
    id: "main",
    name: "<PERSON><PERSON><PERSON> Principal",
    description: "Le serveur principal du Royaume des Tempêtes",
    status: "online",
    type: "main",
    players: { current: 245, max: 500 },
    ping: 15,
    uptime: 99.9,
    region: "Europe",
    version: "1.20.4",
    features: ["Roleplay", "Économie", "Guildes", "Quêtes", "Événements"],
    lastUpdate: "Il y a 2 heures",
    ip: "play.royaume-tempetes.fr",
    port: 25565
  },
  {
    id: "creative",
    name: "Monde Créatif",
    description: "Serveur créatif pour construire et tester",
    status: "online",
    type: "creative",
    players: { current: 67, max: 200 },
    ping: 18,
    uptime: 99.5,
    region: "Europe",
    version: "1.20.4",
    features: ["Mode Créatif", "WorldEdit", "Plots", "Constructions"],
    lastUpdate: "Il y a 1 heure",
    ip: "creative.royaume-tempetes.fr",
    port: 25565
  },
  {
    id: "beta",
    name: "Royaume Beta",
    description: "Serveur de test des nouvelles fonctionnalités",
    status: "maintenance",
    type: "beta",
    players: { current: 0, max: 100 },
    ping: 0,
    uptime: 0,
    region: "Europe",
    version: "1.21.0-beta",
    features: ["Nouvelles Zones", "Quêtes Beta", "Mécaniques Test"],
    lastUpdate: "En maintenance",
    ip: "beta.royaume-tempetes.fr",
    port: 25565,
    maintenanceProgress: 75
  }
]

const getStatusColor = (status: string) => {
  switch (status) {
    case "online": return "text-green-400 bg-green-500/20 border-green-500/30"
    case "maintenance": return "text-yellow-400 bg-yellow-500/20 border-yellow-500/30"
    case "offline": return "text-red-400 bg-red-500/20 border-red-500/30"
    default: return "text-gray-400 bg-gray-500/20 border-gray-500/30"
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case "online": return CheckCircle
    case "maintenance": return AlertCircle
    case "offline": return XCircle
    default: return AlertCircle
  }
}

const getTypeColor = (type: string) => {
  switch (type) {
    case "main": return "blue"
    case "creative": return "green"
    case "beta": return "purple"
    default: return "gray"
  }
}

export default function ServeursPage() {
  const [refreshing, setRefreshing] = useState(false)

  const handleRefresh = async () => {
    setRefreshing(true)
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  return (
    <div>
      <header className="flex h-16 shrink-0 items-center gap-2 border-b border-base-300/50 bg-base-100/95 backdrop-blur supports-[backdrop-filter]:bg-base-100/80 px-4 shadow-sm relative z-10">
        <SidebarTrigger className="-ml-1 hover:bg-primary/10 transition-colors" />
        <Separator orientation="vertical" className="mr-2 h-4 bg-base-300/50" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/" className="text-base-content/60 hover:text-base-content transition-colors">
                Dashboard
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block text-base-content/40" />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-base-content font-medium">Serveurs</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </header>
      <main className="flex-1 p-6 min-h-screen relative overflow-hidden">
        {/* Consistent Background Image */}
        <motion.div
          initial={{ opacity: 0, scale: 1.1 }}
          animate={{ opacity: 0.15, scale: 1 }}
          transition={{ duration: 2 }}
          className="absolute inset-0 z-0"
        >
          <img src="/image1.png" alt="" className="w-full h-full object-cover opacity-15 mix-blend-soft-light" />
        </motion.div>

        <div className="relative z-10 max-w-7xl mx-auto space-y-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="flex items-center justify-between"
          >
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <Server className="h-8 w-8 text-primary" />
                <h1 className="text-4xl font-bold text-base-content">Serveurs du Royaume</h1>
              </div>
              <p className="text-lg text-base-content/70">
                Connectez-vous aux différents mondes du Royaume des Tempêtes
              </p>
            </div>
            <Button
              onClick={handleRefresh}
              disabled={refreshing}
              variant="outline"
              className="border hover:bg-muted"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Actualiser
            </Button>
          </motion.div>

          {/* Server Status Overview */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-4 gap-4"
          >
            <Card className="bg-base-100/95 backdrop-blur border-primary/20 shadow-lg">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-500/20 rounded-lg">
                    <CheckCircle className="h-5 w-5 text-green-400" />
                  </div>
                  <div>
                    <p className="text-sm text-base-content/60">Serveurs en ligne</p>
                    <p className="text-2xl font-bold text-base-content">
                      {serversData.filter(s => s.status === 'online').length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-base-100/95 backdrop-blur border-primary/20 shadow-lg">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-500/20 rounded-lg">
                    <Users className="h-5 w-5 text-blue-400" />
                  </div>
                  <div>
                    <p className="text-sm text-base-content/60">Joueurs connectés</p>
                    <p className="text-2xl font-bold text-base-content">
                      {serversData.reduce((acc, s) => acc + s.players.current, 0)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-base-100/95 backdrop-blur border-primary/20 shadow-lg">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-500/20 rounded-lg">
                    <Activity className="h-5 w-5 text-purple-400" />
                  </div>
                  <div>
                    <p className="text-sm text-base-content/60">Uptime moyen</p>
                    <p className="text-2xl font-bold text-base-content">
                      {(serversData.filter(s => s.status === 'online').reduce((acc, s) => acc + s.uptime, 0) /
                        serversData.filter(s => s.status === 'online').length).toFixed(1)}%
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-base-100/95 backdrop-blur border-primary/20 shadow-lg">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-yellow-500/20 rounded-lg">
                    <Zap className="h-5 w-5 text-yellow-400" />
                  </div>
                  <div>
                    <p className="text-sm text-base-content/60">Ping moyen</p>
                    <p className="text-2xl font-bold text-base-content">
                      {Math.round(serversData.filter(s => s.status === 'online').reduce((acc, s) => acc + s.ping, 0) /
                        serversData.filter(s => s.status === 'online').length)}ms
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Servers List */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="space-y-6"
          >
            {serversData.map((server, index) => {
              const StatusIcon = getStatusIcon(server.status)
              const typeColor = getTypeColor(server.type)

              return (
                <motion.div
                  key={server.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <Card className="bg-base-100/95 backdrop-blur border-primary/20 shadow-lg hover:shadow-xl transition-all duration-300">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-lg border-${typeColor}-500/20 bg-${typeColor}-500/10`}>
                            <Server className={`h-6 w-6 text-${typeColor}-400`} />
                          </div>
                          <div>
                            <div className="flex items-center gap-3">
                              <CardTitle className="text-base-content text-xl">
                                {server.name}
                              </CardTitle>
                              <Badge className={getStatusColor(server.status)}>
                                <StatusIcon className="h-3 w-3 mr-1" />
                                {server.status === 'online' ? 'En ligne' :
                                 server.status === 'maintenance' ? 'Maintenance' : 'Hors ligne'}
                              </Badge>
                            </div>
                            <CardDescription className="text-base-content/70">
                              {server.description}
                            </CardDescription>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {server.status === 'online' && (
                            <Button
                              variant="outline"
                              className={`border-${typeColor}-500/20 hover:bg-${typeColor}-500/10`}
                            >
                              Rejoindre
                            </Button>
                          )}
                          {server.status === 'maintenance' && (
                            <Button variant="outline" disabled>
                              Maintenance
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent>
                      <Tabs defaultValue="stats" className="w-full">
                        <TabsList className="grid w-full grid-cols-3">
                          <TabsTrigger value="stats">Statistiques</TabsTrigger>
                          <TabsTrigger value="features">Fonctionnalités</TabsTrigger>
                          <TabsTrigger value="connection">Connexion</TabsTrigger>
                        </TabsList>

                        <TabsContent value="stats" className="mt-4">
                          {server.status === 'maintenance' ? (
                            <div className="space-y-4">
                              <div className="space-y-2">
                                <div className="flex items-center justify-between text-sm">
                                  <span className="text-base-content/60">Progression de la maintenance</span>
                                  <span className="text-yellow-400">{server.maintenanceProgress}%</span>
                                </div>
                                <Progress value={server.maintenanceProgress} className="h-2" />
                              </div>
                              <p className="text-sm text-base-content/60">
                                Mise à jour en cours : Nouvelles zones et fonctionnalités
                              </p>
                            </div>
                          ) : (
                            <div className="grid gap-4 md:grid-cols-4">
                              <div className={`flex items-center gap-2 p-3 rounded-lg border-${typeColor}-500/20 bg-${typeColor}-500/10`}>
                                <Users className={`h-4 w-4 text-${typeColor}-400`} />
                                <div className="text-sm">
                                  <p className="text-base-content/60">Joueurs</p>
                                  <p className="font-medium text-base-content">
                                    {server.players.current}/{server.players.max}
                                  </p>
                                </div>
                              </div>
                              <div className={`flex items-center gap-2 p-3 rounded-lg border-${typeColor}-500/20 bg-${typeColor}-500/10`}>
                                <Wifi className={`h-4 w-4 text-${typeColor}-400`} />
                                <div className="text-sm">
                                  <p className="text-base-content/60">Latence</p>
                                  <p className="font-medium text-base-content">{server.ping}ms</p>
                                </div>
                              </div>
                              <div className={`flex items-center gap-2 p-3 rounded-lg border-${typeColor}-500/20 bg-${typeColor}-500/10`}>
                                <Clock className={`h-4 w-4 text-${typeColor}-400`} />
                                <div className="text-sm">
                                  <p className="text-base-content/60">Uptime</p>
                                  <p className="font-medium text-base-content">{server.uptime}%</p>
                                </div>
                              </div>
                              <div className={`flex items-center gap-2 p-3 rounded-lg border-${typeColor}-500/20 bg-${typeColor}-500/10`}>
                                <Globe className={`h-4 w-4 text-${typeColor}-400`} />
                                <div className="text-sm">
                                  <p className="text-base-content/60">Région</p>
                                  <p className="font-medium text-base-content">{server.region}</p>
                                </div>
                              </div>
                            </div>
                          )}
                        </TabsContent>

                        <TabsContent value="features" className="mt-4">
                          <div className="grid gap-2 md:grid-cols-2">
                            {server.features.map((feature, featureIndex) => (
                              <div key={featureIndex} className="flex items-center gap-2 p-2 rounded bg-primary/5">
                                <CheckCircle className="h-4 w-4 text-green-400" />
                                <span className="text-sm text-base-content">{feature}</span>
                              </div>
                            ))}
                          </div>
                        </TabsContent>

                        <TabsContent value="connection" className="mt-4">
                          <div className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                              <div className="space-y-2">
                                <label className="text-sm font-medium text-base-content">Adresse IP</label>
                                <div className="flex items-center gap-2">
                                  <code className="flex-1 p-2 bg-muted rounded text-sm font-mono">
                                    {server.ip}
                                  </code>
                                  <Button size="sm" variant="outline">Copier</Button>
                                </div>
                              </div>
                              <div className="space-y-2">
                                <label className="text-sm font-medium text-base-content">Port</label>
                                <div className="flex items-center gap-2">
                                  <code className="flex-1 p-2 bg-muted rounded text-sm font-mono">
                                    {server.port}
                                  </code>
                                  <Button size="sm" variant="outline">Copier</Button>
                                </div>
                              </div>
                            </div>
                            <div className="space-y-2">
                              <label className="text-sm font-medium text-base-content">Version</label>
                              <Badge variant="outline">{server.version}</Badge>
                            </div>
                            <div className="space-y-2">
                              <label className="text-sm font-medium text-base-content">Dernière mise à jour</label>
                              <p className="text-sm text-base-content/60 flex items-center gap-2">
                                <Calendar className="h-4 w-4" />
                                {server.lastUpdate}
                              </p>
                            </div>
                          </div>
                        </TabsContent>
                      </Tabs>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
          </motion.div>
        </div>
      </main>
    </div>
  )
}