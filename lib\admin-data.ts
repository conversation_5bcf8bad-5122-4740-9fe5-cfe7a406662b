// Types pour l'administration
export interface AdminStats {
  totalUsers: number
  activeUsers: number
  pendingTickets: number
  contentPages: number
  serverUptime: number
  dailyVisits: number
}

export interface RecentAction {
  id: number
  user: string
  action: string
  timestamp: string
  type: "edit" | "ticket" | "rule" | "moderation"
}

export interface User {
  id: number
  username: string
  email: string
  role: string
  status: string
  joinDate: string
  lastActive: string
}

export interface Ticket {
  id: number
  title: string
  user: string
  priority: "Critique" | "Haute" | "Normale" | "Basse"
  status: "Ouvert" | "En cours" | "Résolu"
  category: string
  created: string
}

// Données mockup pour l'administration
export const mockStats: AdminStats = {
  totalUsers: 1247,
  activeUsers: 892,
  pendingTickets: 23,
  contentPages: 45,
  serverUptime: 99.8,
  dailyVisits: 3421,
}

export const mockRecentActions: RecentAction[] = [
  {
    id: 1,
    user: "Admin_Sarah",
    action: "Modification de la page Lore - Chapitre 3",
    timestamp: "Il y a 5 minutes",
    type: "edit",
  },
  {
    id: 2,
    user: "Modo_Alex",
    action: "Résolution du ticket #1247",
    timestamp: "Il y a 12 minutes",
    type: "ticket",
  },
  {
    id: 3,
    user: "Admin_Sarah",
    action: "Ajout d'une nouvelle règle PvP",
    timestamp: "Il y a 1 heure",
    type: "rule",
  },
  {
    id: 4,
    user: "Modo_Jordan",
    action: "Modération utilisateur: TempBan_User123",
    timestamp: "Il y a 2 heures",
    type: "moderation",
  },
]

export const mockUsers: User[] = [
  {
    id: 1,
    username: "DragonSlayer_42",
    email: "<EMAIL>",
    role: "Utilisateur",
    status: "En ligne",
    joinDate: "2024-01-15",
    lastActive: "Il y a 2 minutes",
  },
  {
    id: 2,
    username: "MageSupreme",
    email: "<EMAIL>",
    role: "Modérateur",
    status: "En ligne",
    joinDate: "2023-11-20",
    lastActive: "Il y a 5 minutes",
  },
  {
    id: 3,
    username: "KnightOfStorms",
    email: "<EMAIL>",
    role: "Utilisateur",
    status: "Hors ligne",
    joinDate: "2024-02-03",
    lastActive: "Il y a 1 heure",
  },
]

export const mockTickets: Ticket[] = [
  {
    id: 1247,
    title: "Bug d'affichage sur la page Lore",
    user: "TestUser_99",
    priority: "Haute",
    status: "Ouvert",
    category: "Bug",
    created: "2024-01-20 14:30",
  },
  {
    id: 1246,
    title: "Demande d'ajout de fonctionnalité",
    user: "FeatureRequester",
    priority: "Normale",
    status: "En cours",
    category: "Feature",
    created: "2024-01-20 10:15",
  },
  {
    id: 1245,
    title: "Problème de connexion Discord",
    user: "DiscordUser123",
    priority: "Critique",
    status: "Résolu",
    category: "Support",
    created: "2024-01-19 16:45",
  },
]

// Fonction utilitaire pour formater les nombres avec des virgules
export const formatNumber = (num: number): string => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
}