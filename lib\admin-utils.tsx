import { Edit3, <PERSON><PERSON><PERSON>re, FileText, Shield, Activity } from "lucide-react"
import { RecentAction } from "./admin-data"

// Fonction pour obtenir l'icône d'action
export const getActionIcon = (type: RecentAction["type"]) => {
  switch (type) {
    case "edit":
      return <Edit3 className="h-4 w-4 text-blue-500" />
    case "ticket":
      return <MessageSquare className="h-4 w-4 text-green-500" />
    case "rule":
      return <FileText className="h-4 w-4 text-purple-500" />
    case "moderation":
      return <Shield className="h-4 w-4 text-red-500" />
    default:
      return <Activity className="h-4 w-4 text-gray-500" />
  }
}

// Fonction pour obtenir la couleur de priorité
export const getPriorityColor = (priority: string) => {
  switch (priority) {
    case "Critique":
      return "bg-red-500"
    case "Haute":
      return "bg-orange-500"
    case "Normale":
      return "bg-blue-500"
    case "Basse":
      return "bg-gray-500"
    default:
      return "bg-gray-500"
  }
}

// Fonction pour obtenir la couleur de statut
export const getStatusColor = (status: string) => {
  switch (status) {
    case "Ouvert":
      return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
    case "En cours":
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
    case "Résolu":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
  }
}