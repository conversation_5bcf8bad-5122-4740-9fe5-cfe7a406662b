import type { Config } from "tailwindcss";

const config: Config = {
    darkMode: ["class"],
    content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "*.{js,ts,jsx,tsx,mdx}"
  ],
  theme: {
  	extend: {
  		colors: {
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			},
  			sidebar: {
  				DEFAULT: 'hsl(var(--sidebar-background))',
  				foreground: 'hsl(var(--sidebar-foreground))',
  				primary: 'hsl(var(--sidebar-primary))',
  				'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
  				accent: 'hsl(var(--sidebar-accent))',
  				'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
  				border: 'hsl(var(--sidebar-border))',
  				ring: 'hsl(var(--sidebar-ring))'
  			},
  			// Custom color palette
  			indigo: {
  				DEFAULT: '#431072',
  				100: '#0d0316',
  				200: '#1a062d',
  				300: '#270a43',
  				400: '#340d59',
  				500: '#431072',
  				600: '#6919b3',
  				700: '#8f36e2',
  				800: '#b479ec',
  				900: '#dabcf5'
  			},
  			persian_indigo: {
  				DEFAULT: '#3A0C69',
  				100: '#0c0215',
  				200: '#17052a',
  				300: '#23073f',
  				400: '#2f0a54',
  				500: '#3a0c69',
  				600: '#6215af',
  				700: '#892ce6',
  				800: '#b072ef',
  				900: '#d8b9f7'
  			},
  			french_violet: {
  				DEFAULT: '#8634B9',
  				100: '#1b0a25',
  				200: '#351549',
  				300: '#501f6e',
  				400: '#6a2992',
  				500: '#8634b9',
  				600: '#9f53cf',
  				700: '#b77edb',
  				800: '#cfa9e7',
  				900: '#e7d4f3'
  			},
  			floral_white: {
  				DEFAULT: '#FCF7EC',
  				100: '#553e0d',
  				200: '#a97c1a',
  				300: '#e2b044',
  				400: '#efd499',
  				500: '#fcf7ec',
  				600: '#fdf9f1',
  				700: '#fdfbf4',
  				800: '#fefcf8',
  				900: '#fefefb'
  			}
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			}
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out'
  		}
  	}
  },
  plugins: [
    require("tailwindcss-animate"),
    require("@tailwindcss/typography")
  ],
  prefix: "",
  logs: true,
  themeRoot: ":root"
};

export default config;
