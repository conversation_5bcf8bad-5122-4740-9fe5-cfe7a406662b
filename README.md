# 🏰 Le Royaume Des Tempêtes - Roadmap de Développement

## 📋 Vue d'ensemble du projet

Dashboard web pour le serveur de jeu "Le Royaume Des Tempêtes" avec système d'authentification Discord, gestion des joueurs, et interface d'administration complète.

---

## 🎯 Priorités de développement

### 🔥 **Phase 1 - Dashboard Administration Complet** (Priorité CRITIQUE)

#### 1.1 🎛️ Interface d'administration intuitive
- [ ] **Dashboard principal admin** avec vue d'ensemble
- [ ] **Navigation admin dédiée** (sidebar spécialisée)
- [ ] **Système de permissions** granulaire par rôle
- [ ] **Interface responsive** optimisée pour desktop et mobile
- [ ] **Thème admin distinct** avec couleurs et icônes professionnelles

#### 1.2 📝 Éditeur de contenu WYSIWYG
- [ ] **Éditeur de pages Lore** avec prévisualisation en temps réel
- [ ] **Modification des règles** avec formatage riche
- [ ] **Gestion de tous les textes** du site (accueil, à propos, etc.)
- [ ] **Upload et gestion d'images** intégrée avec redimensionnement
- [ ] **Création de nouvelles pages** (Lore, Règles) dynamiquement
- [ ] **Système de brouillons** et versions
- [ ] **Formatage de texte avancé** (gras, italique, couleurs personnalisées)

#### 1.3 🚨 Système de sauvegarde intelligent
- [ ] **Détection des modifications** non sauvegardées
- [ ] **Alerte rouge** avant de quitter sans sauvegarder
- [ ] **Sauvegarde automatique** toutes les 30 secondes
- [ ] **Historique des modifications** avec Supabase (ou Git simple)
- [ ] **Indicateur visuel** de l'état de sauvegarde (sauvé/modifié/en cours)
- [ ] **Restauration de versions** précédentes

#### 1.4 🔧 Fonctionnalités avancées d'administration
- [ ] **Prévisualisation en direct** des modifications
- [ ] **Mode comparaison** avant/après
- [ ] **Système d'approbation** pour les modifications importantes
- [ ] **Logs détaillés** de toutes les actions admin
- [ ] **Notifications push** pour les autres admins lors de modifications
- [ ] **Raccourcis clavier** pour une utilisation rapide (Ctrl+S, Ctrl+Z, etc.)
- [ ] **Interface de recherche** dans tout le contenu
- [ ] **Système de tags** pour organiser le contenu

#### 1.5 📊 Tableau de bord de gestion
- [ ] **Analytics manuelles** configurables selon les préférences client
- [ ] **Métriques personnalisées** (saisie manuelle des données importantes)
- [ ] **Gestion des rôles** Admin et Modérateur avec permissions distinctes
- [ ] **Interface de modération** des utilisateurs
- [ ] **Alertes système** et notifications importantes

---

### 🛠️ **Phase 2 - Infrastructure et Authentification** (Priorité haute)

#### 2.1 🗄️ Configuration Supabase
- [ ] **Setup Supabase** auto-hébergé pour la base de données
- [ ] **Configuration des tables** (utilisateurs, contenu, historique)
- [ ] **Système de backup** automatique Supabase
- [ ] **Migrations de schéma** pour les mises à jour

#### 2.2 🔐 Authentification Logto
- [ ] **Configuration Logto** auto-hébergé
- [ ] **Page de connexion personnalisée** selon les préférences client
- [ ] **Gestion des rôles** (Admin, Modérateur, Utilisateur)
- [ ] **Rate limiting** pour la sécurité des connexions
- [ ] **Sessions sécurisées** avec expiration

#### 2.3 👤 Accès et permissions
- [ ] **Mode visiteur** sans authentification
- [ ] **Limitations d'accès** pour les anonymes
- [ ] **Système de permissions granulaire** Admin/Modo
- [ ] **Transition fluide** anonyme → connecté

---

### 📖 **Phase 3 - Refonte des pages principales** (Priorité moyenne)

#### 3.1 📖 Refonte de la page Lore
- [x] **Système de chapitres** avec navigation par pages
- [x] **Pagination** avec boutons "Suivant/Précédent"
- [x] **Indicateurs de progression** (numéros de pages en bas)
- [x] **Navigation par chapitres** dans une interface moderne
- [ ] **Sauvegarde de progression** de lecture (à implémenter avec la DB)

#### 3.2 📋 Refonte de la page Règlement
- [x] **Interface avec accordéons** (dropdown menus)
- [x] **Catégorisation des règles** (RP, PvP, Général, etc.)
- [x] **Recherche dans les règles**
- [x] **Liens directs** vers des règles spécifiques
- [ ] **Historique des modifications** des règles (à implémenter avec la DB)

#### 3.3 🗂️ Restructuration de la navigation
- [x] **Supprimer la page Guildes** (fonctionnalité reportée)
- [x] **Retirer temporairement PvP** (à réintégrer plus tard)
- [x] **Améliorer la page Serveurs** (focus principal)
- [x] **Optimiser la structure** du menu de navigation

#### 3.4 🎫 Système de tickets complet
- [ ] **Création de tickets** par les joueurs avec formulaire détaillé
- [ ] **Interface admin** de gestion des tickets avec tri et filtres
- [ ] **Statuts des tickets** (ouvert, en cours, fermé, résolu)
- [ ] **Système de réponses** admin/modérateur dans le site
- [ ] **Catégories de tickets** configurables par les admins (Bug, Feature, Support, etc.)
- [ ] **Gestion des raisons** de tickets par les admins
- [ ] **Historique complet** des conversations
- [ ] **Notifications** automatiques pour le staff et les joueurs
- [ ] **Priorités de tickets** (Basse, Normale, Haute, Critique)
- [ ] **Attribution de tickets** à des modérateurs spécifiques

#### 3.5 🗳️ Système de vote
- [ ] **Création de sondages** par les admins
- [ ] **Interface de vote** pour les joueurs connectés
- [ ] **Types de votes** multiples (choix unique, choix multiple, échelle)
- [ ] **Gestion des votes** avec dates de début/fin
- [ ] **Résultats en temps réel** pour les admins
- [ ] **Historique des votes** et statistiques
- [ ] **Restrictions de vote** par rôle si nécessaire
- [ ] **Notifications** pour les nouveaux sondages

---

### 🧪 **Phase 4 - Tests et qualité** (URGENT - Avant intégration BDD)

#### 4.1 🔍 Tests automatisés (CRITIQUE)
- [ ] **Tests unitaires** des composants d'édition (Jest/Vitest)
- [ ] **Tests d'intégration** pour les modifications de contenu
- [ ] **Tests E2E** du workflow admin complet (Playwright)
- [ ] **Tests de sécurité** des permissions Admin/Modo
- [ ] **Tests de sauvegarde** et restauration

#### 4.2 📋 Données de test
- [ ] **Jeu de données** de développement avec Supabase
- [ ] **Utilisateurs de test** (Admin, Modo, User)
- [ ] **Scénarios de test** pour toutes les modifications
- [ ] **Tests de régression** avant chaque déploiement

---

### 🎨 **Phase 5 - Améliorations UX/UI et Performance** (Priorité moyenne)

#### 5.1 📱 Responsive Design (PRIORITÉ)
- [ ] **Interface admin mobile** optimisée
- [ ] **Éditeur responsive** pour tablettes
- [ ] **Navigation adaptative** selon l'écran
- [ ] **Tests sur tous les devices**

#### 5.2 🎨 Personnalisation interface
- [ ] **Mode sombre/clair** pour l'admin
- [ ] **Couleurs personnalisables** pour les textes
- [ ] **Mise en forme avancée** (gras, italique, couleurs)
- [ ] **Thème cohérent** pour l'expérience Roblox

---

### ⚡ **Phase 6 - Performance et optimisation** (Priorité basse)

#### 6.1 🖼️ Optimisation des images
- [ ] **Compression automatique** des uploads
- [ ] **Lazy loading** pour les gros contenus
- [ ] **Images responsives** pour mobile
- [ ] **Cache intelligent** pour les performances

#### 6.2 🔔 Système de notifications
- [ ] **Notifications en temps réel** pour les admins
- [ ] **Messages du staff** aux joueurs
- [ ] **Alertes d'événements** importants
- [ ] **Historique des notifications**

---

## 📋 Organisation du projet

### 🎯 Méthodologie de développement en 3 étapes

#### Étape 1 : 🎨 Création et validation des mockups
- [ ] **Développement de toutes les pages** en mode mockup (données statiques)
- [ ] **Interface complète** sans intégration BDD/services externes
- [ ] **Validation visuelle** et ergonomique de chaque page
- [ ] **Tests utilisateur** sur les mockups pour validation UX
- [ ] **Retours et ajustements** avant passage à l'étape suivante
- [ ] **Documentation** des composants et interfaces créés

#### Étape 2 : 🔌 Intégration des services externes
- [ ] **Configuration Discord** pour l'authentification
- [ ] **Setup Logto** auto-hébergé avec pages personnalisées
- [ ] **Configuration Supabase** auto-hébergé (BDD + Storage)
- [ ] **Tests de connexion** pour chaque service externe
- [ ] **Validation de l'intégration** et stabilité des connexions
- [ ] **Gestion des erreurs** et fallbacks pour chaque service
- [ ] **Documentation** des configurations et APIs

#### Étape 3 : 🔄 Transition mockup vers données réelles
- [ ] **Migration progressive** des données statiques vers dynamiques
- [ ] **Tests d'intégrité** des données à chaque étape
- [ ] **Validation des CRUD** (Create, Read, Update, Delete)
- [ ] **Tests de performance** avec données réelles
- [ ] **Tests de sécurité** et permissions
- [ ] **Tests de régression** complets
- [ ] **Validation finale** avant mise en production

### 📊 Avantages de cette approche
- ✅ **Validation rapide** des concepts sans complexité technique
- ✅ **Réduction des risques** d'intégration
- ✅ **Tests isolés** de chaque composant
- ✅ **Feedback utilisateur** précoce
- ✅ **Développement itératif** et sécurisé

---

## 🤖 IA to IA

### 📝 Mémoire des dernières modifications

Cette section sert de mémoire pour les IAs successives travaillant sur ce projet. Elle contient l'historique des dernières modifications effectuées et les instructions de collaboration.

### 🔧 Instructions de travail

#### Gestionnaire de paquets
- **IMPORTANT**: Ce projet utilise **Bun** comme gestionnaire de paquets, PAS npm ou pnpm
- Pour installer les dépendances: `bun install`
- Pour ajouter des packages: `bun add [package-name]`
- Le serveur de développement est toujours en cours d'exécution, ne pas le relancer

#### Architecture et Bonnes Pratiques
- **JAMAIS créer de fichiers trop long ** sans décomposition
- **TOUJOURS décomposer** en composants réutilisables
- **Utiliser les composants UI** existants dans `/components/ui/`
- **Créer des sous-dossiers** pour organiser les composants par fonctionnalité
- **Séparer la logique métier** des composants d'affichage
- **Éviter la duplication de code** en créant des hooks et utilitaires

#### Méthodologie de collaboration avec l'humain
1. **Réaliser les modifications** selon les instructions du README
2. **Demander confirmation** à l'humain à la fin de chaque tâche
3. **Attendre la validation** : "oui, passe au suivant" ou "erreur à fixer"
4. **Ne passer à la tâche suivante** que si aucune erreur dans la section précédente
5. **Pas d'auto-évaluation** - seul l'humain valide les fonctionnalités
6. **Quand l'humain valide** une fonction qui marche :
   - Ajouter la tâche à la liste des tâches accomplies
   - Mettre à jour cette section avec le contexte pour la prochaine IA

### ✅ Tâches accomplies et validées par l'humain

- **Interface d'administration colorée** : Application du thème coloré de la page classements à la page admin/users
  - Cartes de statistiques avec thèmes colorés (bleu, vert, violet, jaune, orange, rouge)
  - Carte "Filtres et Recherche" avec thème cyan
  - Carte "Liste des Utilisateurs" avec thème indigo
  - Header avec styling cohérent et professionnel
  - Effets visuels : bordures colorées, arrière-plans semi-transparents, backdrop blur, hover effects

- **Décomposition de la page admin principale** : Refactorisation complète du fichier `app/admin/page.tsx`
  - Création de 9 composants modulaires réutilisables dans `/components/admin/`
  - Extraction des données mockup vers `lib/admin-data.ts` avec interfaces TypeScript
  - Création des utilitaires partagés dans `lib/admin-utils.tsx`
  - Amélioration de la maintenabilité et de la lisibilité du code
  - Conservation de toutes les fonctionnalités existantes
  - Architecture modulaire facilitant les tests et les futures modifications

- **Migration vers shadcn/ui pur** : Suppression complète de DaisyUI et migration vers shadcn/ui natif
  - Suppression de la dépendance DaisyUI du projet
  - Remplacement de toutes les classes DaisyUI par des équivalents shadcn/ui
  - Mise à jour de la configuration Tailwind CSS (suppression du plugin DaisyUI)
  - Correction des erreurs de build et de syntaxe CSS
  - Système de couleurs unifié avec les design tokens shadcn/ui
  - Amélioration de la cohérence visuelle et réduction de la taille du bundle

### 📋 Dernières modifications effectuées

- **v0.4.0** (2024-01-XX) : Ajout de la section "IA to IA" dans le README.md
- **v0.4.1** (2024-01-XX) : Correction des instructions pour utiliser Bun au lieu de npm
- **v0.4.2** (2024-01-XX) : Ajout des sections Structure, API, Composants, Performance et Versioning
- **v0.5.0** (2024-01-XX) : Implémentation du thème coloré pour l'interface d'administration
  - Application du design coloré de la page classements à app/admin/users/page.tsx
  - Mise à jour des cartes de statistiques avec thèmes colorés individuels
  - Harmonisation du header avec le style de la page admin principale
  - Amélioration de l'expérience utilisateur avec effets visuels modernes
- **v0.6.0** (2024-01-XX) : Refactorisation majeure de l'architecture admin
  - Décomposition complète du fichier `app/admin/page.tsx` (729 lignes → 50 lignes)
  - Création de 9 composants modulaires dans `/components/admin/`
  - Extraction des données et utilitaires vers `/lib/`
  - Résolution des erreurs TypeScript et amélioration de la structure du code
- **v0.7.0** (2024-01-XX) : Migration complète vers shadcn/ui pur
  - Suppression de DaisyUI et de toutes ses dépendances
  - Remplacement de toutes les classes DaisyUI par des équivalents shadcn/ui
  - Nettoyage de la configuration Tailwind CSS
  - Correction des erreurs de build et de syntaxe CSS
  - Unification du système de couleurs avec les design tokens shadcn/ui
  - Amélioration des performances et réduction de la taille du bundle

### 🔄 Contexte pour la prochaine IA

**Dernière tâche accomplie** : Migration complète vers shadcn/ui pur
- **DaisyUI complètement supprimé** : Dépendance retirée du package.json avec Bun
- **Classes DaisyUI remplacées** : Toutes les classes `bg-base-*`, `text-base-*`, `border-base-*` remplacées
- **Mapping des couleurs** : Migration vers `bg-background`, `bg-card`, `text-foreground`, `text-muted-foreground`, etc.
- **Configuration Tailwind nettoyée** : Plugin DaisyUI retiré, propriétés non-standard supprimées
- **Erreurs de build corrigées** : Problème de "Unclosed block" CSS résolu
- **Système de couleurs unifié** : Utilisation exclusive des design tokens shadcn/ui
- **Build et dev server fonctionnels** : Application accessible sur http://localhost:3001
- **Architecture modulaire préservée** : Tous les composants admin restent fonctionnels
- **Prochaines étapes** : Continuer le développement avec le système shadcn/ui pur ou intégration BDD

---

## 🎨 Migration DaisyUI → shadcn/ui

### 📋 Détails de la migration (v0.7.0)

#### 🔄 Changements effectués
- **Suppression complète de DaisyUI** : Dépendance retirée avec `bun remove daisyui`
- **Configuration Tailwind nettoyée** : Plugin DaisyUI retiré de `tailwind.config.ts`
- **Classes CSS migrées** : Remplacement systématique de toutes les classes DaisyUI

#### 🎯 Mapping des classes principales

| DaisyUI (Ancien) | shadcn/ui (Nouveau) | Usage |
|-------------------|---------------------|-------|
| `bg-base-100` | `bg-background` ou `bg-card` | Arrière-plans principaux |
| `bg-base-300` | `bg-muted` ou `bg-border` | Arrière-plans secondaires |
| `border-base-300` | `border-border` | Bordures |
| `text-base-content` | `text-foreground` ou `text-card-foreground` | Texte principal |
| `text-base-content/60` | `text-muted-foreground` | Texte secondaire |
| `bg-error`, `text-error` | `bg-red-500`, `text-red-500` | États d'erreur |
| `bg-success`, `text-success` | `bg-green-500`, `text-green-500` | États de succès |
| `bg-warning`, `text-warning` | `bg-yellow-500`, `text-yellow-500` | États d'avertissement |
| `bg-info`, `text-info` | `bg-blue-500`, `text-blue-500` | États informatifs |

#### ✅ Avantages de la migration
- **Bundle plus léger** : Suppression d'une dépendance externe
- **Cohérence visuelle** : Design system unifié avec shadcn/ui
- **Maintenance simplifiée** : Moins de conflits entre frameworks CSS
- **Performance améliorée** : Moins de CSS inutilisé
- **Thèmes natifs** : Meilleure intégration avec le système de thèmes shadcn/ui

#### 🔧 Fichiers modifiés
- `package.json` : Suppression de la dépendance DaisyUI
- `tailwind.config.ts` : Retrait du plugin DaisyUI
- `app/globals.css` : Correction des classes dans les scrollbars
- Tous les fichiers de pages : Migration des classes DaisyUI
- Tous les composants : Remplacement des classes de couleur

---

## 🛠️ Stack technique

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS, shadcn/ui (DaisyUI supprimé)
- **UI Components**: shadcn/ui avec design system natif
- **Animations**: Framer Motion
- **Authentification**: Logto auto-hébergé
- **Base de données**: Supabase auto-hébergé
- **Stockage**: Supabase Storage pour les images
- **Déploiement**: Vercel/Netlify
- **Tests**: Jest, Playwright (URGENT avant intégration BDD)
- **Package Manager**: Bun (recommandé)

---

## 📁 Structure des dossiers

### 🏗️ Architecture du projet

```
Le-Royaume-Des-Temp-tes/
├── app/                    # Pages Next.js 14 (App Router)
│   ├── classements/        # Page des classements
│   ├── guildes/           # Page des guildes (temporairement désactivée)
│   ├── login/             # Page de connexion
│   ├── lore/              # Page du lore avec système de chapitres
│   ├── profile/           # Page de profil utilisateur
│   ├── pvp/               # Page PvP (temporairement retirée)
│   ├── reglement/         # Page des règles avec accordéons
│   ├── serveurs/          # Page principale des serveurs
│   ├── settings/          # Page des paramètres
│   ├── layout.tsx         # Layout principal de l'application
│   ├── page.tsx           # Page d'accueil
│   └── globals.css        # Styles globaux
├── components/            # Composants React réutilisables
│   ├── admin/             # Composants spécifiques à l'administration
│   │   ├── admin-header.tsx              # Header de l'interface admin
│   │   ├── admin-welcome-section.tsx     # Section d'accueil avec statistiques
│   │   ├── admin-recent-activity.tsx     # Activité récente
│   │   ├── admin-performance-metrics.tsx # Métriques de performance
│   │   ├── admin-user-management.tsx     # Gestion des utilisateurs
│   │   ├── admin-ticket-management.tsx   # Gestion des tickets
│   │   └── admin-content-management.tsx  # Gestion du contenu
│   ├── ui/                # Composants UI de base (shadcn/ui)
│   ├── app-sidebar.tsx    # Sidebar de navigation principale
│   ├── main-dashboard.tsx # Dashboard principal
│   ├── theme-provider.tsx # Provider pour les thèmes
│   └── theme-toggle.tsx   # Bouton de changement de thème
├── hooks/                 # Hooks React personnalisés
│   ├── use-mobile.tsx     # Hook pour détecter les appareils mobiles
│   └── use-toast.ts       # Hook pour les notifications toast
├── lib/                   # Utilitaires et configurations
│   ├── admin-data.ts      # Données mockup et interfaces pour l'admin
│   ├── admin-utils.tsx    # Utilitaires spécifiques à l'admin
│   └── utils.ts           # Fonctions utilitaires (cn, etc.)
├── public/                # Assets statiques
│   ├── images/            # Images du projet
│   └── logos/             # Logos et icônes
├── styles/                # Styles additionnels
│   └── globals.css        # Styles CSS globaux
└── Configuration files
    ├── tailwind.config.ts # Configuration Tailwind CSS
    ├── next.config.mjs    # Configuration Next.js
    ├── tsconfig.json      # Configuration TypeScript
    ├── components.json    # Configuration shadcn/ui
    └── package.json       # Dépendances et scripts
```

---

## 🔌 API Endpoints

### 🚧 En cours de développement

*Les endpoints API seront documentés lors de l'intégration avec Supabase et Logto*

#### Endpoints prévus :
- **Authentification** : `/api/auth/*` (via Logto)
- **Utilisateurs** : `/api/users/*`
- **Contenu** : `/api/content/*` (Lore, Règles)
- **Tickets** : `/api/tickets/*`
- **Votes** : `/api/votes/*`
- **Admin** : `/api/admin/*`

---

## 🧩 Composants UI

### 📚 Documentation des composants `/components`

#### Composants principaux
- **`app-sidebar.tsx`** : Navigation latérale avec menu responsive
- **`main-dashboard.tsx`** : Dashboard principal avec widgets
- **`theme-provider.tsx`** : Gestion des thèmes sombre/clair
- **`theme-toggle.tsx`** : Bouton de basculement de thème

#### Composants UI de base (`/components/ui/`)
Basés sur **shadcn/ui** avec design system natif (DaisyUI supprimé) :
- **Formulaires** : `form.tsx`, `input.tsx`, `textarea.tsx`, `select.tsx`
- **Navigation** : `navigation-menu.tsx`, `breadcrumb.tsx`, `pagination.tsx`
- **Affichage** : `card.tsx`, `table.tsx`, `accordion.tsx`, `tabs.tsx`
- **Interactions** : `button.tsx`, `dialog.tsx`, `dropdown-menu.tsx`
- **Feedback** : `toast.tsx`, `alert.tsx`, `progress.tsx`
- **Couleurs** : Système unifié avec `background`, `foreground`, `muted`, `primary`, etc.

#### Hooks personnalisés (`/hooks/`)
- **`use-mobile.tsx`** : Détection responsive des appareils mobiles
- **`use-toast.ts`** : Gestion des notifications toast

---

## 📊 Performance

### 🎯 Métriques à surveiller

#### Métriques Core Web Vitals
- **LCP (Largest Contentful Paint)** : < 2.5s
- **FID (First Input Delay)** : < 100ms
- **CLS (Cumulative Layout Shift)** : < 0.1

#### Métriques de performance
- **Time to First Byte (TTFB)** : < 600ms
- **First Contentful Paint (FCP)** : < 1.8s
- **Speed Index** : < 3.4s
- **Total Blocking Time (TBT)** : < 200ms

#### Métriques spécifiques au projet
- **Temps de chargement des pages Lore** : < 2s
- **Temps de recherche dans les règles** : < 500ms
- **Responsive sur mobile** : Score > 90
- **Accessibilité** : Score > 95

#### Outils de monitoring
- **Lighthouse** : Audits automatisés
- **Web Vitals** : Métriques en temps réel
- **Next.js Analytics** : Performance des pages
- **Vercel Analytics** : Métriques de déploiement

---

## 🔢 Versioning

### 📋 Numérotation des versions des modifications

#### Format de versioning
- **Format** : `v[MAJOR].[MINOR].[PATCH]`
- **Exemple** : `v1.2.3`

#### Types de modifications
- **MAJOR** : Changements incompatibles, refonte majeure
- **MINOR** : Nouvelles fonctionnalités compatibles
- **PATCH** : Corrections de bugs, améliorations mineures

#### Historique des versions
- **v0.1.0** : Setup initial du projet Next.js
- **v0.2.0** : Ajout des pages Lore et Règlement
- **v0.3.0** : Implémentation de la navigation et sidebar
- **v0.4.0** : Ajout de la section "IA to IA" dans README
- **v0.5.0** : Interface d'administration colorée
- **v0.6.0** : Refactorisation de l'architecture admin
- **v0.7.0** : Migration complète vers shadcn/ui pur (suppression DaisyUI)

#### Workflow de versioning
1. **Développement** : Branches `feature/*`
2. **Test** : Merge vers `develop`
3. **Release** : Tag de version sur `main`
4. **Documentation** : Mise à jour du CHANGELOG.md

---

## 📅 Timeline estimé

| Phase | Durée estimée | Statut |
|-------|---------------|--------|
| Phase 1 | 4-5 semaines | 🔄 **PRIORITÉ CRITIQUE** |
| Phase 2 | 2-3 semaines | ⏳ À venir |
| Phase 3 | 2-3 semaines | ⏳ À venir |
| Phase 4 | 1-2 semaines | 🚨 **URGENT avant BDD** |
| Phase 5 | 2-3 semaines | ⏳ À venir |
| Phase 6 | 1-2 semaines | ⏳ À venir |

---

## 🚀 Pour commencer

1. **Cloner le repository**
2. **Installer les dépendances**: `bun install`
3. **Configurer l'environnement**: Copier `.env.example` vers `.env.local`
4. **Lancer le serveur de dev**: `bun dev` (généralement déjà en cours)

---

## 📞 Contact

Pour toute question sur le développement, contactez l'équipe de développement.
