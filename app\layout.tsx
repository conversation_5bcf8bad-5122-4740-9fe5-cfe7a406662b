"use client"

import type React from "react"
import type { Metadata } from "next"
import { Inter } from "next/font/google"
import { usePathname } from 'next/navigation'
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { AppSidebar } from "@/components/app-sidebar"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"

const inter = Inter({ subsets: ["latin"] })

// export const metadata: Metadata = {
//   title: "Le Royaume Des Tempêtes #SOON",
//   description: "Dashboard de jeu - Le Royaume Des Tempêtes",
//   generator: 'v0.dev'
// }

const ConditionalLayout = ({ children }: { children: React.ReactNode }) => {
  const pathname = usePathname()
  const isAdminPage = pathname.startsWith('/admin')

  if (isAdminPage) {
    return <>{children}</>
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-base-200 via-base-100 to-base-200">
      <div className="absolute inset-0 bg-[url('/mainbgimg.jpg')] bg-cover bg-center opacity-5" />
      <div className="relative z-10 min-h-screen">
        <SidebarProvider>
          <AppSidebar />
          <SidebarInset>
            <main className="flex-1 w-full overflow-y-auto p-0">
              {children}
            </main>
          </SidebarInset>
        </SidebarProvider>
      </div>
    </div>
  )
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="fr" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider>
          <ConditionalLayout>{children}</ConditionalLayout>
        </ThemeProvider>
      </body>
    </html>
  )
}
