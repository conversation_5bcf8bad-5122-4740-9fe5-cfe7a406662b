"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { Crown, MessageSquare } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

const containerVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      staggerChildren: 0.1,
    },
  },
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
    },
  },
}

export default function LoginPage() {
  const [isLoading, setIsLoading] = React.useState(false)

  const handleDiscordLogin = async () => {
    setIsLoading(true)
    // Simulate Discord OAuth process
    // In a real app, this would redirect to Discord OAuth
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Redirect to Discord OAuth URL
    // window.location.href = "https://discord.com/api/oauth2/authorize?client_id=YOUR_CLIENT_ID&redirect_uri=YOUR_REDIRECT_URI&response_type=code&scope=identify%20email%20guilds"
    
    setIsLoading(false)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-base-200 via-base-100 to-base-200 flex items-center justify-center p-4 relative overflow-hidden">
      <div className="absolute inset-0 bg-[url('/mainbgimg.jpg')] bg-cover bg-center opacity-10" />

      {/* Full Screen Background Images */}
      <motion.div
        initial={{ opacity: 0, scale: 1.1 }}
        animate={{ opacity: 0.15, scale: 1 }}
        transition={{ duration: 2, delay: 0.5 }}
        className="absolute inset-0 z-0"
      >
        <img src="/image1.png" alt="" className="w-full h-full object-cover opacity-30" />
      </motion.div>

      <motion.div
        initial={{ opacity: 0, scale: 1.1 }}
        animate={{ opacity: 0.12, scale: 1 }}
        transition={{ duration: 2, delay: 1 }}
        className="absolute inset-0 z-0"
      >
        <img src="/image2.png" alt="" className="w-full h-full object-cover opacity-25 mix-blend-overlay" />
      </motion.div>

      <motion.div
        initial={{ opacity: 0, scale: 1.1 }}
        animate={{ opacity: 0.18, scale: 1 }}
        transition={{ duration: 2, delay: 1.5 }}
        className="absolute inset-0 z-0"
      >
        <img src="/image3.png" alt="" className="w-full h-full object-cover opacity-35 mix-blend-soft-light" />
      </motion.div>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="w-full max-w-md relative z-10"
      >
        <motion.div
          variants={itemVariants}
          className="text-center mb-8"
        >
          <motion.div
            whileHover={{ scale: 1.05, rotate: 5 }}
            className="w-20 h-20 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center mx-auto mb-6 shadow-lg"
          >
            <Crown className="w-10 h-10 text-primary-content" />
          </motion.div>
          <h1 className="text-3xl font-bold text-base-content mb-2">Le Royaume Des Tempêtes</h1>
          <Badge className="bg-accent/20 text-accent border-accent/30 text-lg px-4 py-1">#SOON</Badge>
          <p className="text-base-content/70 mt-4 text-lg">
            Rejoignez l'aventure épique
          </p>
        </motion.div>

        <motion.div variants={itemVariants}>
          <Card className="bg-base-100/95 backdrop-blur border-base-300 shadow-xl">
            <CardHeader className="text-center space-y-4">
              <CardTitle className="text-2xl">Connexion</CardTitle>
              <CardDescription className="text-base">
                Connectez-vous avec votre compte Discord pour accéder au royaume
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
              <div className="text-center space-y-4">
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    onClick={handleDiscordLogin}
                    className="w-full bg-[#5865F2] hover:bg-[#4752C4] text-white py-6 text-lg font-semibold"
                    disabled={isLoading}
                    size="lg"
                  >
                    {isLoading ? (
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        className="w-6 h-6 border-2 border-white border-t-transparent rounded-full mr-3"
                      />
                    ) : (
                      <MessageSquare className="w-6 h-6 mr-3" />
                    )}
                    {isLoading ? "Connexion en cours..." : "Se connecter avec Discord"}
                  </Button>
                </motion.div>

                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t border-base-300" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-base-100 px-2 text-base-content/60">Pourquoi Discord ?</span>
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-3 text-sm text-base-content/70">
                  <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                    <div className="w-2 h-2 bg-success rounded-full"></div>
                    <span>Connexion sécurisée et rapide</span>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                    <div className="w-2 h-2 bg-success rounded-full"></div>
                    <span>Accès direct à notre serveur Discord</span>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                    <div className="w-2 h-2 bg-success rounded-full"></div>
                    <span>Synchronisation automatique des rôles</span>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                    <div className="w-2 h-2 bg-success rounded-full"></div>
                    <span>Pas besoin de créer un nouveau compte</span>
                  </div>
                </div>
              </div>

              <div className="text-center">
                <p className="text-xs text-base-content/60">
                  En vous connectant, vous acceptez nos{" "}
                  <a href="/reglement" className="text-primary hover:underline">
                    conditions d'utilisation
                  </a>
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          variants={itemVariants}
          className="text-center mt-6"
        >
          <p className="text-base-content/60 text-sm">
            Pas encore sur notre Discord ?{" "}
            <a
              href="https://discord.gg/cbQbt6jHTS"
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:underline font-medium"
            >
              Rejoignez-nous maintenant
            </a>
          </p>
        </motion.div>
      </motion.div>
    </div>
  )
}